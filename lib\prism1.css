/* PrismJS 1.16.0
https://prismjs.com/download.html#themes=prism&languages=markup+css+clike+javascript+c+cpp+markup-templating+less+java+php+json+kotlin+sql+scss+python+jsx+sass&plugins=line-highlight */
/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou


MIT LICENSE

Copyright (c) 2012 Lea Verou

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.



 */

code[class*="language-"],
pre[class*="language-"] {
	color: black;
	background: none;
/*	text-shadow: 0 1px white; nope*/
	font-family: Consolas, 'Source Code Pro', Menlo, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
	font-size: 1em;
	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	word-wrap: normal;
	line-height: 1.5;

	-moz-tab-size: 2;
	-o-tab-size: 2;
	tab-size: 2;

	-webkit-hyphens: none;
	-moz-hyphens: none;
	-ms-hyphens: none;
	hyphens: none;
}

pre[class*="language-"]::-moz-selection, pre[class*="language-"] ::-moz-selection,
code[class*="language-"]::-moz-selection, code[class*="language-"] ::-moz-selection {
	text-shadow: none;
	background: #b3d4fc;
}

pre[class*="language-"]::selection, pre[class*="language-"] ::selection,
code[class*="language-"]::selection, code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}
pre[class*="language-"] code em {
  font-style: italic;
}

@media print {
	code[class*="language-"],
	pre[class*="language-"] {
		text-shadow: none;
	}
}

/* Code blocks */
pre[class*="language-"] {
	padding: 1em;
	margin: 1em 0;
	overflow: auto;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
	background: #f5f2f0;
	background: #f1f1f1;
}

.w3-example pre[class*="language-"] {
	background: #f5f2f0;
	background: #f1f1f1;
  border-left: 4px solid #43259fc9;
}


/* Inline code */
:not(pre) > code[class*="language-"] {
	padding: .1em;
	border-radius: .3em;
	white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: slategray;
}

.token.punctuation {
	color: #999;
}

.namespace {
	opacity: .7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
	color: #905;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
	color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
	color: #9a6e3a;
	/*background: hsla(0, 0%, 100%, .5);*/
}

.token.atrule,
.token.attr-value,
.token.keyword {
	color: #07a;
}

.token.function,
.token.class-name {
	color: #DD4A68;
}

.token.regex,
.token.important,
.token.variable {
	color: #e90;
  color: #DD4A68;
}

.token.important,
.token.bold {
	font-weight: bold;
}
.token.italic {
	font-style: italic;
}

.token.entity {
	cursor: help;
}

pre[data-line] {
	position: relative;
	bpadding: 1em 0 1em 3em;
}

.line-highlight {
	position: absolute;
	left: 10px;
	right: 10px;
	left: 0;
	right: 0;
	dpadding: inherit 0;
	margin-top: 1em; /* Same as .prisms padding-top */

	background: hsla(270, 100%, 60%,.08);
	background: hsla(0, 0%, 70%,.08);
/*	background: linear-gradient(to right, hsla(0, 0%, 50%,.08) 70%, hsla(24, 20%, 50%,0));*/

	pointer-events: none;

	line-height: inherit;
	white-space: pre;
}

/*	.line-highlight:before,
	.line-highlight[data-end]:after {
		content: attr(data-start);
		position: absolute;
		top: .4em;
		left: .6em;
		min-width: 1em;
		padding: 0 .5em;
		background-color: hsla(24, 20%, 50%,.4);
		color: hsl(24, 20%, 95%);
		font: bold 65%/1.5 sans-serif;
		text-align: center;
		vertical-align: .3em;
		border-radius: 999px;
		text-shadow: none;
		box-shadow: 0 1px white;
	}

	.line-highlight[data-end]:after {
		content: attr(data-end);
		top: auto;
		bottom: .4em;
	}
*/
.line-numbers .line-highlight:before,
.line-numbers .line-highlight:after {
	content: none;
}



/*Dark Theme:*/

.darktheme code[class*="language-"],
.darktheme pre[class*="language-"] {
	color: white;
  background-color: rgb(40,44,52)!important;
}
.darktheme .token.comment,
.darktheme .token.prolog,
.darktheme .token.doctype,
.darktheme .token.cdata {
  color: #b2b2b2;
}

.darktheme .token.punctuation {
  color: #88c6be;
}

.darktheme .namespace {
	opacity: .7;
}

.darktheme .token.property,
.darktheme .token.tag,
.darktheme .token.boolean,
.darktheme .token.number,
.darktheme .token.constant,
.darktheme .token.symbol,
.darktheme .token.deleted {
  color: #fc9ca7;
}

.darktheme .token.selector,
.darktheme .token.attr-name,
.darktheme .token.string,
.darktheme .token.char,
.darktheme .token.builtin,
.darktheme .token.inserted {
  color: #87c58b;
}

.darktheme .token.operator,
.darktheme .token.entity,
.darktheme .token.url,
.darktheme .language-css .token.string,
.darktheme .style .token.string {
  color: #dee3ed;
/*	background: hsla(0, 0%, 100%, .5);*/
}

.darktheme .token.atrule,
.darktheme .token.attr-value,
.darktheme .token.keyword {
  color: #c5a5c5;
}

.darktheme .token.function {
  color: #74b3f1;
}
.darktheme .token.class-name {
  color: #faca6b;
}

.darktheme .token.regex,
.darktheme .token.important,
.darktheme .token.variable {
  color: #fc8391;
}

.darktheme .token.important,
.darktheme .token.bold {
	font-weight: bold;
}
.darktheme .token.italic {
	font-style: italic;
}

