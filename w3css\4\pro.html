
<!DOCTYPE html>
<html lang="en-US">
<head>
<title>404 - Page not found</title>
<meta charset="windows-1252">
<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
<meta name="Keywords" content="HTML,CSS,XML,JavaScript,DOM,jQuery,PHP,SQL,colors,tutorial,programming,development,training,learning,quiz,primer,lessons,reference,examples,source code,demos,tips,color table,w3c,cascading style sheets,active server pages,Web building,Webmaster">
<meta name="Description" content="HTML XHTML CSS JavaScript XML XSL SQL Tutorials References Examples" />
<link rel="stylesheet" type="text/css" href="/w3css/3/w3.css" />

<script>
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
ga('create', 'UA-3855518-1', 'auto');
ga('require', 'displayfeatures');
ga('send', 'pageview');
</script>

<style type="text/css">
#goog-wm h3.closest-match,#goog-wm h3.other-things
{
font-family:verdana,helvetica,arial,sans-serif;
font-size:120%;
}
#goog-wm ul li
{
font-family:verdana,helvetica,arial,sans-serif;
font-size:120%;
margin-top:3px;
}
#goog-wm h3.closest-match a
{
font-family:verdana,helvetica,arial,sans-serif;
}
body{background-color:#ffffff;padding:13px;}
</style>

</head>
<body>
<div class="w3-container w3-green" style="padding:32px 64px">
<h1>Sorry</h1>
<h1>404 - The page cannot be found</h1>
</div>
<div class="w3-container w3-light-grey">
<p>We cannot find the page you are looking for.</p>
<p>It might have been removed, had its name changed, or is temporarily unavailable. </p>
<p>Please check that the Web site address is spelled correctly.</p>
<p>Or go to our 
<a href="/default.asp">home page</a>, and use the menus to navigate to a specific section.</p> 
</div>

<script type="text/javascript">
  var GOOG_FIXURL_LANG = 'en';
  var GOOG_FIXURL_SITE = 'https://www.w3schools.com'
</script>
<script type="text/javascript" src="https://linkhelp.clients.google.com/tbproxy/lh/wm/fixurl.js">
</script>
<script>
var sitemap = document.getElementsByTagName('li');
for (var i = 0; i < sitemap.length; i++) {
    if (sitemap[i].innerHTML.indexOf("sitemap") > -1) {
        document.getElementsByTagName('li')[i].style.display="none";
    }
}
</script>
</body>
</html>